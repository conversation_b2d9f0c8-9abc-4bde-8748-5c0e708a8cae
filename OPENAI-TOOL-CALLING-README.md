# OpenAI Tool Calling with NVD API

This implementation uses **OpenAI's function calling** with the **AI SDK** and **Zod schemas** to create an intelligent vulnerability research assistant that can automatically call NVD API functions based on natural language queries.

## 🚀 Setup

### 1. Install Dependencies

The required packages are already installed:
- `ai` - AI SDK for tool calling
- `zod` - Schema validation
- `@ai-sdk/openai` - OpenAI provider (install when ready)

### 2. Set OpenAI API Key

```bash
export OPENAI_API_KEY="your-openai-api-key-here"
```

### 3. Install OpenAI Provider (when ready)

```bash
npm install @ai-sdk/openai
```

## 🛠 Tool Definitions

All tools are defined using the AI SDK's `tool()` function with Zod schemas:

```typescript
import { tool } from 'ai';
import { z } from 'zod';

const nvdTools = {
  getCVEById: tool({
    description: "Get detailed information about a specific CVE by its identifier",
    parameters: z.object({
      cveId: z.string().describe("The CVE identifier (e.g., CVE-2021-44228)")
    }),
    execute: async ({ cveId }) => {
      // NVD API call implementation
    }
  }),

  searchCVEs: tool({
    description: "Search for CVEs with various filters like keywords, severity, date ranges",
    parameters: z.object({
      keyword: z.string().optional().describe("Search keyword or phrase"),
      severity: z.enum(["LOW", "MEDIUM", "HIGH", "CRITICAL"]).optional(),
      days: z.number().optional().describe("Number of days back to search"),
      hasKev: z.boolean().optional().describe("Filter for CISA KEV catalog"),
      limit: z.number().default(10).describe("Maximum results to return")
    }),
    execute: async ({ keyword, severity, days, hasKev, limit }) => {
      // Search implementation
    }
  })
  // ... more tools
};
```

## 🤖 OpenAI Integration

The main chat handler uses OpenAI with tool calling:

```typescript
import { generateText } from 'ai';
import { openai } from '@ai-sdk/openai';

async function handleChatMessage(message: string) {
  const result = await generateText({
    model: openai('gpt-4'),
    messages: [
      {
        role: 'system',
        content: 'You are a cybersecurity expert assistant that helps users find and analyze vulnerability information from the National Vulnerability Database (NVD).'
      },
      {
        role: 'user',
        content: message
      }
    ],
    tools: nvdTools,
    maxToolRoundtrips: 3,
  });

  return {
    message: result.text,
    toolCalls: result.toolCalls,
    usage: result.usage
  };
}
```

## 📡 API Usage

### Start the Server
```bash
npm run dev
```

### Chat with Natural Language
```bash
# Get specific CVE
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Tell me about CVE-2021-44228"}'

# Search vulnerabilities
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Find critical Microsoft vulnerabilities from the last 30 days"}'

# Get CISA KEV vulnerabilities
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Show me the most dangerous vulnerabilities that are being actively exploited"}'
```

## 🔧 Available Tools

### 1. getCVEById
Get detailed information about a specific CVE.

**Parameters:**
- `cveId` (string): CVE identifier like "CVE-2021-44228"

**Example queries:**
- "Tell me about CVE-2021-44228"
- "Get details for CVE-2023-1234"
- "What is CVE-2022-5678?"

### 2. searchCVEs
Search CVEs with various filters.

**Parameters:**
- `keyword` (optional): Search terms
- `severity` (optional): "LOW", "MEDIUM", "HIGH", "CRITICAL"
- `days` (optional): Number of days back to search
- `hasKev` (optional): Filter for CISA KEV catalog
- `limit` (optional): Max results (default 10)

**Example queries:**
- "Find Microsoft vulnerabilities"
- "Show me critical vulnerabilities from last week"
- "Search for remote code execution vulnerabilities"

### 3. getCriticalCVEs
Get critical severity vulnerabilities from recent days.

**Parameters:**
- `days` (optional): Number of days back (default 30)

**Example queries:**
- "Show me critical vulnerabilities"
- "What are the most severe vulnerabilities this month?"
- "Find critical CVEs from last 7 days"

### 4. getKEVVulnerabilities
Get CISA Known Exploited Vulnerabilities.

**Parameters:**
- `limit` (optional): Max results (default 10)

**Example queries:**
- "Show me actively exploited vulnerabilities"
- "What vulnerabilities are in the CISA KEV catalog?"
- "Find the most dangerous vulnerabilities"

## 🎯 Example Conversations

### Complex Query
**User:** "I need to know about critical Microsoft Windows vulnerabilities from the last 2 weeks that are being actively exploited"

**AI Response:** The AI will:
1. Parse the intent: critical + Microsoft Windows + last 2 weeks + actively exploited
2. Call `searchCVEs` with: `{keyword: "Microsoft Windows", severity: "CRITICAL", days: 14, hasKev: true}`
3. Format and explain the results

### Follow-up Questions
**User:** "Tell me more about the first one"

**AI Response:** The AI will:
1. Remember the previous CVE ID from context
2. Call `getCVEById` with the specific CVE
3. Provide detailed analysis

## 🔄 Fallback Mode

When OpenAI API key is not available, the system falls back to pattern matching:

```typescript
// Fallback uses simple pattern detection
if (message.includes("CVE-")) {
  // Extract CVE ID and call getCVEById
}
if (message.includes("critical")) {
  // Call getCriticalCVEs
}
// etc.
```

## 📊 Response Format

### With OpenAI
```json
{
  "message": "I found 15 critical Microsoft Windows vulnerabilities from the last 2 weeks...",
  "toolCalls": [
    {
      "toolCallId": "call_123",
      "toolName": "searchCVEs",
      "args": {
        "keyword": "Microsoft Windows",
        "severity": "CRITICAL",
        "days": 14
      },
      "result": {
        "total": 15,
        "results": [...]
      }
    }
  ],
  "usage": {
    "promptTokens": 150,
    "completionTokens": 200,
    "totalTokens": 350
  }
}
```

### Fallback Mode
```json
{
  "message": "Here are the critical vulnerabilities from the last 30 days:",
  "toolCall": {
    "tool": "getCriticalCVEs",
    "parameters": { "days": 30 },
    "result": { "total": 45, "results": [...] }
  }
}
```

## 🧪 Testing

### Test OpenAI Integration
```bash
# Set API key first
export OPENAI_API_KEY="your-key"

# Run tests
npm run test-chat

# Run OpenAI demo
npx ts-node openai-chat.ts
```

### Test Fallback Mode
```bash
# Don't set API key
unset OPENAI_API_KEY

# Run tests - will use pattern matching
npm run test-chat
```

## 🔐 Environment Variables

```bash
# Required for OpenAI integration
OPENAI_API_KEY="sk-..."

# Optional: NVD API key for higher rate limits
NVD_API_KEY="your-nvd-api-key"
```

## 🚀 Advanced Usage

### Custom System Prompt
```typescript
const result = await generateText({
  model: openai('gpt-4'),
  messages: [
    {
      role: 'system',
      content: `You are a senior cybersecurity analyst specializing in vulnerability research. 
      When analyzing CVEs, always consider:
      - CVSS score and severity
      - Whether it's in CISA KEV catalog
      - Potential impact and exploitability
      - Recommended mitigations`
    },
    { role: 'user', content: message }
  ],
  tools: nvdTools,
  maxToolRoundtrips: 5,
});
```

### Multiple Tool Calls
The AI can make multiple tool calls in sequence:
1. Search for vulnerabilities
2. Get details on specific CVEs
3. Check if they're in KEV catalog
4. Provide comprehensive analysis

## 📁 File Structure

```
├── openai-chat.ts        # Main OpenAI tool calling implementation
├── app.ts               # Express server with chat endpoint
├── nvd-api.ts           # NVD API client
├── chat-demo.ts         # Fallback pattern matching (legacy)
└── test-chat-api.ts     # Test suite
```

## 🔮 Next Steps

1. **Install OpenAI Provider**: `npm install @ai-sdk/openai`
2. **Set API Key**: Get OpenAI API key and set environment variable
3. **Test Integration**: Run tests with real OpenAI API
4. **Customize Prompts**: Adjust system prompts for your use case
5. **Add More Tools**: Extend with additional NVD API functions

---

**Ready for intelligent vulnerability research with OpenAI! 🤖🔍**
