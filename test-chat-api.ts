#!/usr/bin/env ts-node

/**
 * Test script for the NVD AI Chat API
 * This demonstrates how to interact with the chat endpoint
 */

import axios from 'axios';

const API_BASE = 'http://localhost:3000';

async function testChatAPI() {
  console.log('🧪 Testing NVD AI Chat API');
  console.log('='.repeat(50));

  const testMessages = [
    "Hello, what can you do?",
    "Show me critical vulnerabilities",
    "Get CVE-2021-44228 details",
    "Search for Microsoft vulnerabilities",
    "Show KEV vulnerabilities"
  ];

  for (const message of testMessages) {
    try {
      console.log(`\n💬 Sending: "${message}"`);
      
      const response = await axios.post(`${API_BASE}/api/chat`, {
        message: message
      }, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 30000 // 30 second timeout
      });

      console.log(`✅ Response: ${response.data.message}`);
      
      if (response.data.toolCall) {
        const toolCall = response.data.toolCall;
        console.log(`🔧 Tool Used: ${toolCall.tool}`);
        console.log(`📋 Parameters:`, JSON.stringify(toolCall.parameters, null, 2));
        
        if (toolCall.result.error) {
          console.log(`❌ Error: ${toolCall.result.error}`);
        } else {
          console.log(`✅ Tool Results:`);
          
          if (toolCall.result.results) {
            console.log(`   Found ${toolCall.result.count || 0} of ${toolCall.result.total || 0} total`);
            toolCall.result.results.slice(0, 2).forEach((item: any, index: number) => {
              console.log(`   ${index + 1}. ${item.id} - Score: ${item.cvssScore || 'N/A'}`);
              if (item.description) {
                console.log(`      ${item.description.substring(0, 80)}...`);
              }
            });
          } else if (toolCall.result.id) {
            // Single CVE result
            const cve = toolCall.result;
            console.log(`   CVE: ${cve.id}`);
            console.log(`   Score: ${cve.cvssScore || 'N/A'}`);
            console.log(`   Status: ${cve.status}`);
            console.log(`   KEV: ${cve.isInKEV ? 'Yes' : 'No'}`);
            console.log(`   Description: ${cve.description.substring(0, 100)}...`);
          }
        }
      }
      
      console.log('\n' + '-'.repeat(50));
      
      // Add delay between requests
      await new Promise(resolve => setTimeout(resolve, 2000));
      
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNREFUSED') {
          console.log('❌ Server not running. Please start the server first with: npm run dev');
          break;
        } else if (error.response) {
          console.log(`❌ HTTP ${error.response.status}: ${error.response.data?.error || error.message}`);
        } else {
          console.log(`❌ Network error: ${error.message}`);
        }
      } else {
        console.log(`❌ Error: ${error}`);
      }
    }
  }
}

async function testHealthEndpoint() {
  try {
    console.log('\n🏥 Testing health endpoint...');
    const response = await axios.get(`${API_BASE}/api/health`);
    console.log('✅ Health check passed:', response.data);
  } catch (error) {
    console.log('❌ Health check failed:', error);
  }
}

async function testExamplesEndpoint() {
  try {
    console.log('\n📚 Testing examples endpoint...');
    const response = await axios.get(`${API_BASE}/api/examples`);
    console.log('✅ Examples retrieved:');
    response.data.examples.forEach((example: any, index: number) => {
      console.log(`   ${index + 1}. "${example.message}"`);
      console.log(`      ${example.description}`);
    });
  } catch (error) {
    console.log('❌ Examples endpoint failed:', error);
  }
}

// Curl examples for manual testing
function showCurlExamples() {
  console.log('\n📋 Manual Testing with curl:');
  console.log('='.repeat(50));
  
  const examples = [
    {
      description: "Get critical vulnerabilities",
      command: `curl -X POST ${API_BASE}/api/chat \\
  -H "Content-Type: application/json" \\
  -d '{"message": "Show me critical vulnerabilities"}'`
    },
    {
      description: "Get specific CVE details",
      command: `curl -X POST ${API_BASE}/api/chat \\
  -H "Content-Type: application/json" \\
  -d '{"message": "Get CVE-2021-44228 details"}'`
    },
    {
      description: "Search for vulnerabilities",
      command: `curl -X POST ${API_BASE}/api/chat \\
  -H "Content-Type: application/json" \\
  -d '{"message": "Search for Microsoft vulnerabilities"}'`
    }
  ];
  
  examples.forEach((example, index) => {
    console.log(`\n${index + 1}. ${example.description}:`);
    console.log(example.command);
  });
}

async function main() {
  console.log('🚀 NVD AI Chat API Test Suite');
  console.log('Make sure the server is running with: npm run dev\n');
  
  await testHealthEndpoint();
  await testExamplesEndpoint();
  await testChatAPI();
  
  showCurlExamples();
  
  console.log('\n✅ Test suite completed!');
  console.log('\n📝 Summary:');
  console.log('   - The chat endpoint accepts natural language queries');
  console.log('   - It automatically selects and calls appropriate NVD API tools');
  console.log('   - Results are formatted and returned with tool call details');
  console.log('   - Rate limiting is handled automatically');
}

// Run tests if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

export { testChatAPI, testHealthEndpoint, testExamplesEndpoint };
