# NVD AI Chat Server with OpenAI Tool Calling

A complete implementation of **OpenAI tool calling** with the **AI SDK** and **Zod schemas** for intelligent vulnerability research using the National Vulnerability Database (NVD) API.

## 🚀 Quick Start

```bash
# Install OpenAI provider (when ready)
npm install @ai-sdk/openai

# Set your OpenAI API key
export OPENAI_API_KEY="your-openai-api-key-here"

# Start the server (already running)
# npm run dev

# Test the implementation
npm run test-tools
```

## 🤖 What This Does

This creates an AI assistant that can:
- **Understand natural language** queries about vulnerabilities
- **Automatically select and call** appropriate NVD API functions
- **Use proper OpenAI tool calling** with Zod schemas
- **Provide intelligent responses** with vulnerability analysis

## 📡 Single Chat Endpoint

**POST /api/chat** - Ask anything about vulnerabilities in natural language

```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Find critical Microsoft vulnerabilities from last 2 weeks"}'
```

## 🛠 Available Tools (with <PERSON><PERSON>)

### 1. getCVEById
```typescript
tool({
  description: "Get detailed information about a specific CVE",
  parameters: z.object({
    cveId: z.string().describe("CVE identifier like CVE-2021-44228")
  })
})
```

### 2. searchCVEs  
```typescript
tool({
  description: "Search CVEs with filters",
  parameters: z.object({
    keyword: z.string().optional(),
    severity: z.enum(["LOW", "MEDIUM", "HIGH", "CRITICAL"]).optional(),
    days: z.number().optional(),
    hasKev: z.boolean().optional(),
    limit: z.number().default(10)
  })
})
```

### 3. getCriticalCVEs
```typescript
tool({
  description: "Get critical severity CVEs from recent days",
  parameters: z.object({
    days: z.number().default(30)
  })
})
```

### 4. getKEVVulnerabilities
```typescript
tool({
  description: "Get CISA Known Exploited Vulnerabilities",
  parameters: z.object({
    limit: z.number().default(10)
  })
})
```

## 💬 Example Queries

- **"Tell me about CVE-2021-44228"** → Calls `getCVEById`
- **"Find critical Microsoft vulnerabilities"** → Calls `searchCVEs` with filters
- **"Show me actively exploited vulnerabilities"** → Calls `getKEVVulnerabilities`
- **"What are the most severe vulnerabilities this week?"** → Calls `getCriticalCVEs`

## 🧪 Testing

```bash
# Test tools directly
npm run test-tools

# Test chat API
npm run test-chat

# Test OpenAI integration
npm run openai-demo
```

## 📁 Key Files

- **`openai-chat.ts`** - Main OpenAI tool calling implementation
- **`app.ts`** - Express server with single chat endpoint
- **`nvd-api.ts`** - Complete NVD API client
- **`test-openai-tools.ts`** - Tool testing suite

## 🔧 Implementation Details

The AI uses proper tool calling with the AI SDK:

```typescript
import { generateText, tool } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';

const result = await generateText({
  model: openai('gpt-4'),
  messages: [
    { role: 'system', content: 'You are a cybersecurity expert...' },
    { role: 'user', content: userMessage }
  ],
  tools: nvdTools,
  maxToolRoundtrips: 3
});
```

## 🔄 Fallback Mode

Without OpenAI API key, uses pattern matching for basic functionality.

## 📚 Documentation

- [OpenAI Tool Calling Guide](./OPENAI-TOOL-CALLING-README.md)
- [NVD API Documentation](./NVD-API-README.md)
- [AI Chat Guide](./AI-CHAT-README.md)

---

**Ready for intelligent vulnerability research with OpenAI! 🤖🔍**

Note: Servers are already running - do not run `npm run dev` or `npm run start`
