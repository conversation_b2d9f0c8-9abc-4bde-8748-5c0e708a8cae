#!/usr/bin/env ts-node

/**
 * Test script to demonstrate OpenAI tool calling with NVD API
 * Shows how the tools work both with and without OpenAI API key
 */

import { nvdTools } from './openai-chat';

async function testToolsDirectly() {
  console.log('🧪 Testing NVD Tools Directly');
  console.log('='.repeat(50));

  try {
    // Test 1: getCVEById tool
    console.log('\n1️⃣ Testing getCVEById tool...');
    const cveResult = await nvdTools.getCVEById.execute({ cveId: 'CVE-2021-44228' });
    
    if (cveResult.error) {
      console.log(`❌ Error: ${cveResult.error}`);
    } else {
      console.log(`✅ Found CVE: ${cveResult.id}`);
      console.log(`   Score: ${cveResult.cvssScore}`);
      console.log(`   Status: ${cveResult.status}`);
      console.log(`   In KEV: ${cveResult.isInKEV ? 'Yes' : 'No'}`);
      console.log(`   Description: ${cveResult.description.substring(0, 100)}...`);
    }

    // Test 2: searchCVEs tool
    console.log('\n2️⃣ Testing searchCVEs tool...');
    const searchResult = await nvdTools.searchCVEs.execute({
      keyword: 'Microsoft',
      severity: 'HIGH',
      limit: 3
    });
    
    if (searchResult.error) {
      console.log(`❌ Error: ${searchResult.error}`);
    } else {
      console.log(`✅ Found ${searchResult.count} of ${searchResult.total} Microsoft HIGH severity CVEs`);
      searchResult.results.forEach((cve: any, index: number) => {
        console.log(`   ${index + 1}. ${cve.id} - Score: ${cve.cvssScore || 'N/A'}`);
      });
    }

    // Test 3: getCriticalCVEs tool
    console.log('\n3️⃣ Testing getCriticalCVEs tool...');
    const criticalResult = await nvdTools.getCriticalCVEs.execute({ days: 30 });
    
    if (criticalResult.error) {
      console.log(`❌ Error: ${criticalResult.error}`);
    } else {
      console.log(`✅ Found ${criticalResult.results.length} critical CVEs from last ${criticalResult.days} days`);
      criticalResult.results.slice(0, 2).forEach((cve: any, index: number) => {
        console.log(`   ${index + 1}. ${cve.id} - Score: ${cve.cvssScore}`);
        console.log(`      KEV: ${cve.isInKEV ? 'Yes' : 'No'}`);
      });
    }

    // Test 4: getKEVVulnerabilities tool
    console.log('\n4️⃣ Testing getKEVVulnerabilities tool...');
    const kevResult = await nvdTools.getKEVVulnerabilities.execute({ limit: 3 });
    
    if (kevResult.error) {
      console.log(`❌ Error: ${kevResult.error}`);
    } else {
      console.log(`✅ Found ${kevResult.count} of ${kevResult.total} KEV vulnerabilities`);
      kevResult.results.forEach((cve: any, index: number) => {
        console.log(`   ${index + 1}. ${cve.id} - Score: ${cve.cvssScore || 'N/A'}`);
        if (cve.cisaActionDue) {
          console.log(`      🚨 Action Due: ${new Date(cve.cisaActionDue).toLocaleDateString()}`);
        }
      });
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

async function demonstrateToolSchemas() {
  console.log('\n📋 Tool Schemas (Zod Definitions)');
  console.log('='.repeat(50));

  // Show the tool definitions
  console.log('\n🔧 getCVEById:');
  console.log('   Description: Get detailed information about a specific CVE');
  console.log('   Parameters: { cveId: string }');
  console.log('   Example: { cveId: "CVE-2021-44228" }');

  console.log('\n🔧 searchCVEs:');
  console.log('   Description: Search CVEs with filters');
  console.log('   Parameters: {');
  console.log('     keyword?: string,');
  console.log('     severity?: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL",');
  console.log('     days?: number,');
  console.log('     hasKev?: boolean,');
  console.log('     limit?: number (default: 10)');
  console.log('   }');
  console.log('   Example: { keyword: "Microsoft", severity: "HIGH", days: 30 }');

  console.log('\n🔧 getCriticalCVEs:');
  console.log('   Description: Get critical severity CVEs from recent days');
  console.log('   Parameters: { days?: number (default: 30) }');
  console.log('   Example: { days: 7 }');

  console.log('\n🔧 getKEVVulnerabilities:');
  console.log('   Description: Get CISA Known Exploited Vulnerabilities');
  console.log('   Parameters: { limit?: number (default: 10) }');
  console.log('   Example: { limit: 5 }');
}

function showOpenAIIntegrationExample() {
  console.log('\n🤖 OpenAI Integration Example');
  console.log('='.repeat(50));

  console.log(`
// How to use with OpenAI (when @ai-sdk/openai is installed):

import { generateText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { nvdTools } from './openai-chat';

const result = await generateText({
  model: openai('gpt-4'),
  messages: [
    {
      role: 'system',
      content: 'You are a cybersecurity expert assistant.'
    },
    {
      role: 'user', 
      content: 'Find critical Microsoft vulnerabilities from last week'
    }
  ],
  tools: nvdTools,
  maxToolRoundtrips: 3
});

// The AI will automatically:
// 1. Parse the user intent
// 2. Call searchCVEs with appropriate parameters
// 3. Format and explain the results
`);
}

function showCurlExamples() {
  console.log('\n📡 API Usage Examples');
  console.log('='.repeat(50));

  const examples = [
    {
      description: "Ask about a specific CVE",
      query: "Tell me about CVE-2021-44228",
      curl: `curl -X POST http://localhost:3000/api/chat \\
  -H "Content-Type: application/json" \\
  -d '{"message": "Tell me about CVE-2021-44228"}'`
    },
    {
      description: "Search with complex criteria",
      query: "Find critical Microsoft vulnerabilities from last 2 weeks",
      curl: `curl -X POST http://localhost:3000/api/chat \\
  -H "Content-Type: application/json" \\
  -d '{"message": "Find critical Microsoft vulnerabilities from last 2 weeks"}'`
    },
    {
      description: "Get actively exploited vulnerabilities",
      query: "Show me the most dangerous vulnerabilities being exploited",
      curl: `curl -X POST http://localhost:3000/api/chat \\
  -H "Content-Type: application/json" \\
  -d '{"message": "Show me the most dangerous vulnerabilities being exploited"}'`
    }
  ];

  examples.forEach((example, index) => {
    console.log(`\n${index + 1}. ${example.description}:`);
    console.log(`   Query: "${example.query}"`);
    console.log(`   Command:`);
    console.log(`   ${example.curl}`);
  });
}

async function main() {
  console.log('🚀 OpenAI Tool Calling Test Suite for NVD API');
  console.log('This demonstrates how the tools work with proper Zod schemas\n');

  // Test the tools directly
  await testToolsDirectly();
  
  // Show tool schemas
  demonstrateToolSchemas();
  
  // Show OpenAI integration example
  showOpenAIIntegrationExample();
  
  // Show API usage examples
  showCurlExamples();

  console.log('\n✅ Test suite completed!');
  console.log('\n📝 Next Steps:');
  console.log('   1. Install OpenAI provider: npm install @ai-sdk/openai');
  console.log('   2. Set API key: export OPENAI_API_KEY="your-key"');
  console.log('   3. Start server: npm run dev');
  console.log('   4. Test with curl commands above');
  console.log('\n🔄 Current Mode: Fallback (pattern matching)');
  console.log('   With OpenAI API key: Full AI tool calling');
}

// Run tests if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}
