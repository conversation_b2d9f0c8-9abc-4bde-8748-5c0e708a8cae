# NVD AI Chat Server

A single AI chat endpoint that uses tool calling to interact with the National Vulnerability Database (NVD) API. Ask questions in natural language and get vulnerability information through intelligent tool selection.

## 🚀 Quick Start

1. **Start the server:**
   ```bash
   npm run dev
   ```

2. **Test the chat endpoint:**
   ```bash
   curl -X POST http://localhost:3000/api/chat \
     -H "Content-Type: application/json" \
     -d '{"message": "Show me critical vulnerabilities"}'
   ```

3. **Run automated tests:**
   ```bash
   npm run test-chat
   ```

## 🤖 How It Works

The AI chat server uses **tool calling** to automatically:

1. **Parse Intent** - Understands what you're asking for from natural language
2. **Select Tools** - Chooses the appropriate NVD API function to call
3. **Execute Tools** - Calls the NVD API with extracted parameters
4. **Format Results** - Returns structured vulnerability data

### Available Tools

- **`getCVEById`** - Get detailed information about a specific CVE
- **`searchCVEs`** - Search CVEs with keywords, severity, date filters
- **`getCriticalCVEs`** - Get critical severity vulnerabilities
- **`getKEVVulnerabilities`** - Get CISA Known Exploited Vulnerabilities

## 📡 API Endpoints

### POST /api/chat
Main chat endpoint that accepts natural language queries.

**Request:**
```json
{
  "message": "Show me critical vulnerabilities"
}
```

**Response:**
```json
{
  "message": "Here are the critical vulnerabilities from the last 30 days:",
  "toolCall": {
    "tool": "getCriticalCVEs",
    "parameters": { "days": 30 },
    "result": {
      "total": 45,
      "days": 30,
      "results": [
        {
          "id": "CVE-2024-1234",
          "published": "2024-01-15T10:00:00.000Z",
          "description": "Critical vulnerability in...",
          "cvssScore": 9.8,
          "isInKEV": true
        }
      ]
    }
  },
  "timestamp": "2024-01-20T12:00:00.000Z",
  "success": true
}
```

### GET /api/health
Health check endpoint.

### GET /api/examples
Get example messages and available tools.

## 💬 Example Queries

### Get Specific CVE
```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Get CVE-2021-44228 details"}'
```

**What it does:** Calls `getCVEById` tool with extracted CVE ID.

### Search by Keyword
```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Search for Microsoft vulnerabilities"}'
```

**What it does:** Calls `searchCVEs` tool with keyword "Microsoft".

### Get Critical Vulnerabilities
```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Show me critical vulnerabilities"}'
```

**What it does:** Calls `getCriticalCVEs` tool for last 30 days.

### Get CISA KEV Vulnerabilities
```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Show KEV vulnerabilities"}'
```

**What it does:** Calls `getKEVVulnerabilities` tool.

### Advanced Queries
```bash
# Time-based search
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Find high severity CVEs from last 7 days"}'

# Specific product search
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Search for Apache vulnerabilities"}'
```

## 🧪 Testing

### Automated Tests
```bash
# Test the chat API
npm run test-chat

# Test NVD API functions directly
npm run test-nvd

# Run chat demo
npm run chat-demo
```

### Manual Testing
1. Start the server: `npm run dev`
2. Open http://localhost:3000 in your browser
3. Use the curl examples above
4. Or use a tool like Postman/Insomnia

## 🔧 Tool Calling Implementation

The chat handler uses simple pattern matching to detect intent:

```typescript
// Example: CVE ID detection
if (lowerMessage.includes("cve-") && lowerMessage.match(/cve-\d{4}-\d+/)) {
  const cveMatch = message.match(/CVE-\d{4}-\d+/i);
  if (cveMatch) {
    const cveId = cveMatch[0];
    const result = await nvdTools.getCVEById(cveId);
    return {
      message: `Here's information about ${cveId}:`,
      toolCall: { tool: "getCVEById", parameters: { cveId }, result }
    };
  }
}
```

### Extending with Real AI Models

To use with actual AI models (OpenAI, Anthropic, etc.), replace the pattern matching with:

```typescript
import { generateText, tool } from "ai";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";

// Define tools with Zod schemas
const tools = {
  getCVEById: tool({
    description: "Get detailed information about a specific CVE",
    parameters: z.object({
      cveId: z.string().describe("CVE identifier like CVE-2021-44228")
    }),
    execute: async ({ cveId }) => {
      // Your NVD API call here
    }
  })
};

// Use with AI model
const result = await generateText({
  model: openai('gpt-4'),
  messages: [{ role: 'user', content: userMessage }],
  tools,
  maxToolRoundtrips: 3
});
```

## 🔑 Rate Limiting

The NVD API has rate limits:
- **Without API Key**: 5 requests per 30 seconds
- **With API Key**: 50 requests per 30 seconds

The client automatically handles rate limiting with appropriate delays.

## 📊 Response Format

All chat responses include:

- **`message`** - Human-readable response
- **`toolCall`** - Details about the tool that was called (if any)
  - `tool` - Name of the tool used
  - `parameters` - Parameters passed to the tool
  - `result` - Raw result from the tool
- **`timestamp`** - When the response was generated
- **`success`** - Whether the request was successful

## 🚨 Error Handling

The API handles various error scenarios:

```json
{
  "error": "Message is required",
  "example": { "message": "Show me critical vulnerabilities" }
}
```

Common errors:
- Missing message in request body
- NVD API rate limit exceeded
- Network connectivity issues
- Invalid CVE IDs

## 🔮 Future Enhancements

1. **Real AI Integration** - Replace pattern matching with actual AI models
2. **Conversation Memory** - Remember context across multiple messages
3. **Advanced Filtering** - More sophisticated query parsing
4. **Caching** - Cache frequent queries for better performance
5. **Webhooks** - Real-time vulnerability alerts
6. **Authentication** - User-specific API keys and rate limits

## 📁 File Structure

```
├── app.ts                 # Main Express server with chat endpoint
├── chat-demo.ts          # Chat handler with tool calling logic
├── nvd-api.ts            # NVD API client with all functions
├── test-chat-api.ts      # Test suite for chat endpoint
├── test-nvd-api.ts       # Test suite for NVD API functions
└── nvd-examples.ts       # Comprehensive NVD API examples
```

## 🌐 Web Interface

Visit http://localhost:3000 for a simple web interface with example queries.

## 📚 Related Documentation

- [NVD API Documentation](./NVD-API-README.md) - Complete NVD API client documentation
- [NVD Official Docs](https://nvd.nist.gov/developers/vulnerabilities) - Official NVD API documentation
- [AI SDK Documentation](https://ai-sdk.dev/docs/ai-sdk-core/tools-and-tool-calling) - Tool calling with AI SDK

---

**Ready to chat with vulnerabilities? Start the server and ask away! 🤖🔍**
