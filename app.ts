import express from "express";
import { handleChatMessage } from "./chat-demo";

const app = express();
const port = 3000;

// Middleware
app.use(express.json());

app.get("/", (_req, res) => {
  res.send(`
    <h1>🤖 NVD AI Chat Server</h1>
    <p>Ask me about vulnerabilities!</p>
    <h2>Try these examples:</h2>
    <ul>
      <li>POST /api/chat with: <code>{"message": "Show me critical vulnerabilities"}</code></li>
      <li>POST /api/chat with: <code>{"message": "Get CVE-2021-44228 details"}</code></li>
      <li>POST /api/chat with: <code>{"message": "Search for Microsoft vulnerabilities"}</code></li>
      <li>POST /api/chat with: <code>{"message": "Show KEV vulnerabilities"}</code></li>
    </ul>
  `);
});

// AI Chat endpoint with tool calling
// @ts-expect-error
app.post("/api/chat", async (req, res) => {
  try {
    const { message } = req.body;

    if (!message) {
      return res.status(400).json({
        error: "Message is required",
        example: { message: "Show me critical vulnerabilities" },
      });
    }

    console.log(`💬 Received message: "${message}"`);

    // Use the chat handler from chat-demo.ts
    const result = await handleChatMessage(message);

    res.json({
      message: result.message,
      toolCall: result.toolCall || null,
      timestamp: new Date().toISOString(),
      success: true,
    });
  } catch (error) {
    console.error("Error in chat:", error);
    res.status(500).json({
      error: "Failed to process chat message",
      details: error instanceof Error ? error.message : String(error),
    });
  }
});

// Health check endpoint
app.get("/api/health", (_req, res) => {
  res.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    service: "NVD AI Chat Server",
    endpoints: {
      chat: "POST /api/chat",
      health: "GET /api/health",
    },
  });
});

// Example endpoint to show what the chat can do
app.get("/api/examples", (_req, res) => {
  res.json({
    examples: [
      {
        message: "Show me critical vulnerabilities",
        description: "Get critical severity CVEs from the last 30 days",
      },
      {
        message: "Get CVE-2021-44228 details",
        description: "Get detailed information about a specific CVE",
      },
      {
        message: "Search for Microsoft vulnerabilities",
        description: "Search for CVEs containing specific keywords",
      },
      {
        message: "Show KEV vulnerabilities",
        description:
          "Get vulnerabilities from CISA's Known Exploited Vulnerabilities catalog",
      },
      {
        message: "Find high severity CVEs from last 7 days",
        description: "Search with severity and time filters",
      },
    ],
    availableTools: [
      "getCVEById - Get specific CVE details",
      "searchCVEs - Search with various filters",
      "getCriticalCVEs - Get critical vulnerabilities",
      "getKEVVulnerabilities - Get CISA KEV vulnerabilities",
    ],
  });
});

app.listen(port, () => {
  console.log(`🤖 NVD AI Chat Server is running on port ${port}`);
  console.log(`📖 Available endpoints:`);
  console.log(`   POST /api/chat - Chat with AI about vulnerabilities`);
  console.log(`   GET /api/health - Health check`);
  console.log(`   GET /api/examples - See example messages`);
  console.log(`   GET / - Web interface`);
  console.log(`\n💬 Try sending a POST request to /api/chat with:`);
  console.log(`   curl -X POST http://localhost:${port}/api/chat \\`);
  console.log(`     -H "Content-Type: application/json" \\`);
  console.log(`     -d '{"message": "Show me critical vulnerabilities"}'`);
  console.log(
    `\n🌐 Open http://localhost:${port} in your browser for more info`
  );
});
