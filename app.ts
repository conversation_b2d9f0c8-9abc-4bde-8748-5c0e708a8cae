import express from "express";
import type { Request, Response } from "express";
import { nvdApi, NVDUtils, CVESearchParams } from "./nvd-api";

const app = express();
const port = 3000;

// Middleware
app.use(express.json());

app.get("/", (_req, res) => {
  res.send("NVD API Server - Vulnerability Database Interface");
});

// Get a specific CVE by ID
// @ts-expect-error
app.get("/api/cve/:cveId", async (req, res) => {
  try {
    const { cveId } = req.params;
    const response = await nvdApi.getCVEById(cveId);

    if (response.vulnerabilities.length === 0) {
      return res.status(404).json({ error: "CVE not found" });
    }

    const cve = response.vulnerabilities[0].cve;
    const result = {
      id: cve.id,
      published: cve.published,
      lastModified: cve.lastModified,
      status: cve.vulnStatus,
      description: NVDUtils.getPrimaryDescription(cve),
      cvssScore: NVDUtils.getHighestCVSSScore(cve),
      isInKEV: NVDUtils.isInKEV(cve),
      references: cve.references.slice(0, 5), // Limit to first 5 references
      weaknesses:
        cve.weaknesses?.map((w) => w.description[0]?.value).filter(Boolean) ||
        [],
    };

    res.json(result);
  } catch (error) {
    console.error("Error fetching CVE:", error);
    res.status(500).json({ error: "Failed to fetch CVE data" });
  }
});

// Search CVEs with various filters
app.get("/api/search", async (req: Request, res: Response) => {
  try {
    const {
      keyword,
      severity,
      days,
      hasKev,
      limit = 20,
      offset = 0,
    } = req.query;

    const params: CVESearchParams = {
      resultsPerPage: Math.min(Number(limit), 100), // Max 100 per request
      startIndex: Number(offset),
    };

    // Add keyword search
    if (keyword) {
      params.keywordSearch = String(keyword);
    }

    // Add severity filter
    if (
      severity &&
      ["LOW", "MEDIUM", "HIGH", "CRITICAL"].includes(String(severity))
    ) {
      params.cvssV3Severity = severity as any;
    }

    // Add date range filter
    if (days) {
      const dateRange = NVDUtils.getLastNDays(Number(days));
      params.pubStartDate = dateRange.startDate;
      params.pubEndDate = dateRange.endDate;
    }

    // Add KEV filter
    if (hasKev === "true") {
      params.hasKev = true;
    }

    const response = await nvdApi.searchCVEs(params);

    const results = response.vulnerabilities.map((vuln) => {
      const cve = vuln.cve;
      return {
        id: cve.id,
        published: cve.published,
        description:
          NVDUtils.getPrimaryDescription(cve).substring(0, 200) + "...",
        cvssScore: NVDUtils.getHighestCVSSScore(cve),
        isInKEV: NVDUtils.isInKEV(cve),
        status: cve.vulnStatus,
      };
    });

    res.json({
      total: response.totalResults,
      count: results.length,
      offset: response.startIndex,
      results,
    });
  } catch (error) {
    console.error("Error searching CVEs:", error);
    res.status(500).json({ error: "Failed to search CVE data" });
  }
});

// Get critical vulnerabilities from recent days
app.get("/api/critical", async (req: Request, res: Response) => {
  try {
    const days = Number(req.query.days) || 30;
    const dateRange = NVDUtils.getLastNDays(days);

    const response = await nvdApi.getCVEsBySeverity("CRITICAL", 3, {
      pubStartDate: dateRange.startDate,
      pubEndDate: dateRange.endDate,
      resultsPerPage: 50,
    });

    const results = response.vulnerabilities.map((vuln) => {
      const cve = vuln.cve;
      return {
        id: cve.id,
        published: cve.published,
        description:
          NVDUtils.getPrimaryDescription(cve).substring(0, 150) + "...",
        cvssScore: NVDUtils.getHighestCVSSScore(cve),
        isInKEV: NVDUtils.isInKEV(cve),
      };
    });

    res.json({
      total: response.totalResults,
      days,
      results,
    });
  } catch (error) {
    console.error("Error fetching critical CVEs:", error);
    res.status(500).json({ error: "Failed to fetch critical CVE data" });
  }
});

// Get CISA KEV vulnerabilities
app.get("/api/kev", async (req: Request, res: Response) => {
  try {
    const limit = Math.min(Number(req.query.limit) || 20, 100);

    const response = await nvdApi.searchCVEs({
      hasKev: true,
      resultsPerPage: limit,
    });

    const results = response.vulnerabilities.map((vuln) => {
      const cve = vuln.cve;
      return {
        id: cve.id,
        published: cve.published,
        description:
          NVDUtils.getPrimaryDescription(cve).substring(0, 150) + "...",
        cvssScore: NVDUtils.getHighestCVSSScore(cve),
        cisaActionDue: cve.cisaActionDue,
        cisaRequiredAction: cve.cisaRequiredAction,
        cisaVulnerabilityName: cve.cisaVulnerabilityName,
      };
    });

    res.json({
      total: response.totalResults,
      count: results.length,
      results,
    });
  } catch (error) {
    console.error("Error fetching KEV CVEs:", error);
    res.status(500).json({ error: "Failed to fetch KEV CVE data" });
  }
});

// Get CVE change history
app.get("/api/cve/:cveId/history", async (req: Request, res: Response) => {
  try {
    const { cveId } = req.params;
    const response = await nvdApi.getCVEHistoryById(cveId);

    const history = response.cveChanges.map((changeWrapper) => {
      const change = changeWrapper.change;
      return {
        eventName: change.eventName,
        created: change.created,
        sourceIdentifier: change.sourceIdentifier,
        details: change.details.map((detail) => ({
          action: detail.action,
          type: detail.type,
          oldValue: detail.oldValue?.substring(0, 100),
          newValue: detail.newValue?.substring(0, 100),
        })),
      };
    });

    res.json({
      cveId,
      totalChanges: response.totalResults,
      history,
    });
  } catch (error) {
    console.error("Error fetching CVE history:", error);
    res.status(500).json({ error: "Failed to fetch CVE history" });
  }
});

// Health check endpoint
app.get("/api/health", (_req, res) => {
  res.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    service: "NVD API Server",
  });
});

// endpoint to run ai and tool calling

app.listen(port, () => {
  console.log(`🚀 NVD API Server is running on port ${port}`);
  console.log(`📖 Available endpoints:`);
  console.log(`   GET /api/cve/:cveId - Get specific CVE`);
  console.log(`   GET /api/search - Search CVEs with filters`);
  console.log(`   GET /api/critical - Get critical vulnerabilities`);
  console.log(`   GET /api/kev - Get CISA KEV vulnerabilities`);
  console.log(`   GET /api/cve/:cveId/history - Get CVE change history`);
  console.log(`   GET /api/health - Health check`);
});
