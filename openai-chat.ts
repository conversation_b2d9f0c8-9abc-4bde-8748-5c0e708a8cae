#!/usr/bin/env ts-node

/**
 * OpenAI Tool Calling implementation for NVD API
 * Uses the AI SDK with proper tool definitions and zod schemas
 */

import { generateText, tool } from 'ai';
import { z } from 'zod';
import { nvdApi, NVDUtils } from './nvd-api';

// Define tools using AI SDK tool() function with zod schemas
const nvdTools = {
  getCVEById: tool({
    description: "Get detailed information about a specific CVE by its identifier",
    parameters: z.object({
      cveId: z.string().describe("The CVE identifier (e.g., CVE-2021-44228)")
    }),
    execute: async ({ cveId }) => {
      try {
        const response = await nvdApi.getCVEById(cveId);
        if (response.vulnerabilities.length === 0) {
          return { error: "CVE not found" };
        }
        
        const cve = response.vulnerabilities[0].cve;
        return {
          id: cve.id,
          published: cve.published,
          lastModified: cve.lastModified,
          status: cve.vulnStatus,
          description: NVDUtils.getPrimaryDescription(cve),
          cvssScore: NVDUtils.getHighestCVSSScore(cve),
          isInKEV: NVDUtils.isInKEV(cve),
          references: cve.references.slice(0, 3),
          weaknesses: cve.weaknesses?.map(w => w.description[0]?.value).filter(Boolean) || []
        };
      } catch (error) {
        return { error: `Failed to fetch CVE: ${error}` };
      }
    }
  }),

  searchCVEs: tool({
    description: "Search for CVEs with various filters like keywords, severity, date ranges",
    parameters: z.object({
      keyword: z.string().optional().describe("Search keyword or phrase"),
      severity: z.enum(["LOW", "MEDIUM", "HIGH", "CRITICAL"]).optional().describe("CVSS severity level"),
      days: z.number().optional().describe("Number of days back to search (e.g., 30 for last 30 days)"),
      hasKev: z.boolean().optional().describe("Filter for CISA KEV catalog vulnerabilities"),
      limit: z.number().default(10).describe("Maximum number of results to return (max 20)")
    }),
    execute: async ({ keyword, severity, days, hasKev, limit = 10 }) => {
      try {
        const searchParams: any = {
          resultsPerPage: Math.min(limit, 20)
        };

        if (keyword) searchParams.keywordSearch = keyword;
        if (severity) searchParams.cvssV3Severity = severity;
        if (hasKev) searchParams.hasKev = true;
        
        if (days) {
          const dateRange = NVDUtils.getLastNDays(days);
          searchParams.pubStartDate = dateRange.startDate;
          searchParams.pubEndDate = dateRange.endDate;
        }

        const response = await nvdApi.searchCVEs(searchParams);
        
        return {
          total: response.totalResults,
          count: response.vulnerabilities.length,
          results: response.vulnerabilities.map(vuln => {
            const cve = vuln.cve;
            return {
              id: cve.id,
              published: cve.published,
              description: NVDUtils.getPrimaryDescription(cve).substring(0, 200) + "...",
              cvssScore: NVDUtils.getHighestCVSSScore(cve),
              isInKEV: NVDUtils.isInKEV(cve),
              status: cve.vulnStatus
            };
          })
        };
      } catch (error) {
        return { error: `Failed to search CVEs: ${error}` };
      }
    }
  }),

  getCriticalCVEs: tool({
    description: "Get critical severity CVEs from recent days",
    parameters: z.object({
      days: z.number().default(30).describe("Number of days back to search for critical CVEs")
    }),
    execute: async ({ days = 30 }) => {
      try {
        const dateRange = NVDUtils.getLastNDays(days);
        const response = await nvdApi.getCVEsBySeverity("CRITICAL", 3, {
          pubStartDate: dateRange.startDate,
          pubEndDate: dateRange.endDate,
          resultsPerPage: 20
        });

        return {
          total: response.totalResults,
          days,
          results: response.vulnerabilities.map(vuln => {
            const cve = vuln.cve;
            return {
              id: cve.id,
              published: cve.published,
              description: NVDUtils.getPrimaryDescription(cve).substring(0, 150) + "...",
              cvssScore: NVDUtils.getHighestCVSSScore(cve),
              isInKEV: NVDUtils.isInKEV(cve)
            };
          })
        };
      } catch (error) {
        return { error: `Failed to fetch critical CVEs: ${error}` };
      }
    }
  }),

  getKEVVulnerabilities: tool({
    description: "Get vulnerabilities from CISA's Known Exploited Vulnerabilities (KEV) catalog",
    parameters: z.object({
      limit: z.number().default(10).describe("Maximum number of KEV vulnerabilities to return")
    }),
    execute: async ({ limit = 10 }) => {
      try {
        const response = await nvdApi.searchCVEs({
          hasKev: true,
          resultsPerPage: Math.min(limit, 20)
        });

        return {
          total: response.totalResults,
          count: response.vulnerabilities.length,
          results: response.vulnerabilities.map(vuln => {
            const cve = vuln.cve;
            return {
              id: cve.id,
              published: cve.published,
              description: NVDUtils.getPrimaryDescription(cve).substring(0, 150) + "...",
              cvssScore: NVDUtils.getHighestCVSSScore(cve),
              cisaActionDue: cve.cisaActionDue,
              cisaRequiredAction: cve.cisaRequiredAction,
              cisaVulnerabilityName: cve.cisaVulnerabilityName
            };
          })
        };
      } catch (error) {
        return { error: `Failed to fetch KEV CVEs: ${error}` };
      }
    }
  })
};

// Mock OpenAI provider for demonstration (replace with real provider when available)
const mockOpenAI = {
  chat: {
    completions: {
      create: async (params: any) => {
        // This is a mock implementation
        // In real usage, you would use: import { openai } from '@ai-sdk/openai';
        throw new Error("OpenAI provider not available. Please install @ai-sdk/openai and set OPENAI_API_KEY");
      }
    }
  }
};

/**
 * Handle chat message using OpenAI with tool calling
 * This function demonstrates how to use the AI SDK with proper tool calling
 */
async function handleChatMessageWithOpenAI(message: string, apiKey?: string) {
  if (!apiKey) {
    // Fallback to pattern matching if no API key
    return handleChatMessageFallback(message);
  }

  try {
    // This is how you would use it with a real OpenAI provider:
    /*
    import { openai } from '@ai-sdk/openai';
    
    const result = await generateText({
      model: openai('gpt-4'),
      messages: [
        {
          role: 'system',
          content: 'You are a cybersecurity expert assistant that helps users find and analyze vulnerability information from the National Vulnerability Database (NVD). Use the available tools to answer questions about CVEs, security vulnerabilities, and threat intelligence.'
        },
        {
          role: 'user',
          content: message
        }
      ],
      tools: nvdTools,
      maxToolRoundtrips: 3,
    });

    return {
      message: result.text,
      toolCalls: result.toolCalls,
      usage: result.usage
    };
    */
    
    throw new Error("OpenAI provider not configured. Set OPENAI_API_KEY environment variable.");
    
  } catch (error) {
    console.error("OpenAI API error:", error);
    // Fallback to pattern matching
    return handleChatMessageFallback(message);
  }
}

/**
 * Fallback chat handler using pattern matching
 * This is used when OpenAI is not available
 */
async function handleChatMessageFallback(message: string) {
  const lowerMessage = message.toLowerCase();
  console.log(`🤖 Processing (fallback mode): "${message}"`);
  
  // Detect intent and call appropriate tools
  if (lowerMessage.includes("cve-") && lowerMessage.match(/cve-\d{4}-\d+/)) {
    const cveMatch = message.match(/CVE-\d{4}-\d+/i);
    if (cveMatch) {
      const cveId = cveMatch[0];
      console.log(`🔧 Calling tool: getCVEById(${cveId})`);
      const result = await nvdTools.getCVEById.execute({ cveId });
      return {
        message: `Here's information about ${cveId}:`,
        toolCall: { tool: "getCVEById", parameters: { cveId }, result }
      };
    }
  }
  
  if (lowerMessage.includes("critical") && (lowerMessage.includes("vulnerabilities") || lowerMessage.includes("cves"))) {
    console.log(`🔧 Calling tool: getCriticalCVEs(30)`);
    const result = await nvdTools.getCriticalCVEs.execute({ days: 30 });
    return {
      message: "Here are the critical vulnerabilities from the last 30 days:",
      toolCall: { tool: "getCriticalCVEs", parameters: { days: 30 }, result }
    };
  }
  
  if (lowerMessage.includes("kev") || lowerMessage.includes("known exploited")) {
    console.log(`🔧 Calling tool: getKEVVulnerabilities(10)`);
    const result = await nvdTools.getKEVVulnerabilities.execute({ limit: 10 });
    return {
      message: "Here are vulnerabilities from CISA's Known Exploited Vulnerabilities catalog:",
      toolCall: { tool: "getKEVVulnerabilities", parameters: { limit: 10 }, result }
    };
  }
  
  if (lowerMessage.includes("search") || lowerMessage.includes("find")) {
    const keywords = message.replace(/search|find|for|vulnerabilities|cves/gi, '').trim();
    if (keywords) {
      console.log(`🔧 Calling tool: searchCVEs({ keyword: "${keywords}", limit: 10 })`);
      const result = await nvdTools.searchCVEs.execute({ keyword: keywords, limit: 10 });
      return {
        message: `Here are CVEs related to "${keywords}":`,
        toolCall: { tool: "searchCVEs", parameters: { keyword: keywords, limit: 10 }, result }
      };
    }
  }
  
  // Default response
  return {
    message: `I can help you with vulnerability information! Try asking me:
    
🔍 "Search for Microsoft vulnerabilities"
🚨 "Show me critical vulnerabilities"
📋 "Get CVE-2021-44228 details"
⚠️  "Show KEV vulnerabilities"
📈 "Find high severity CVEs from last 7 days"

Note: For full AI capabilities, set OPENAI_API_KEY environment variable.`,
    toolCall: null
  };
}

// Export the main handler function
export async function handleChatMessage(message: string) {
  const apiKey = process.env.OPENAI_API_KEY;
  
  if (apiKey) {
    return handleChatMessageWithOpenAI(message, apiKey);
  } else {
    return handleChatMessageFallback(message);
  }
}

// Export tools for direct use
export { nvdTools };

// Demo function
async function runOpenAIDemo() {
  console.log('🤖 OpenAI Tool Calling Demo for NVD API');
  console.log('='.repeat(50));
  
  const testMessages = [
    "Show me critical vulnerabilities",
    "Get CVE-2021-44228 details",
    "Search for Microsoft vulnerabilities"
  ];
  
  for (const message of testMessages) {
    try {
      console.log(`\n💬 Testing: "${message}"`);
      const response = await handleChatMessage(message);
      console.log(`🤖 Response: ${response.message}`);
      
      if (response.toolCall) {
        console.log(`🔧 Tool: ${response.toolCall.tool}`);
        console.log(`📋 Parameters:`, response.toolCall.parameters);
      }
      
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      console.error(`❌ Error: ${error}`);
    }
  }
}

// Run demo if executed directly
if (require.main === module) {
  runOpenAIDemo().catch(console.error);
}
