{"name": "vendor_ai", "version": "1.0.0", "main": "app.ts", "scripts": {"start": "node app.js", "dev": "nodemon app.ts", "test-nvd": "npx ts-node test-nvd-api.ts", "test-chat": "npx ts-node test-chat-api.ts", "test-tools": "npx ts-node test-openai-tools.ts", "examples": "npx ts-node nvd-examples.ts", "chat-demo": "npx ts-node chat-demo.ts", "openai-demo": "npx ts-node openai-chat.ts", "build": "tsc"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/express": "^5.0.3", "typescript": "^5.8.3"}, "dependencies": {"ai": "^4.3.16", "axios": "^1.10.0", "express": "^5.1.0", "zod": "^3.25.67"}}