#!/usr/bin/env ts-node

/**
 * OpenAI Tool Calling implementation for NVD API
 * Uses the AI SDK with proper tool definitions and zod schemas
 */

import { generateText, tool } from "ai";
import { z } from "zod";
import { nvdApi, NVDUtils } from "./nvd-api";

// Define tool functions that can be called by AI
const nvdTools = {
  async getCVEById(cveId: string) {
    try {
      const response = await nvdApi.getCVEById(cveId);
      if (response.vulnerabilities.length === 0) {
        return { error: "CVE not found" };
      }

      const cve = response.vulnerabilities[0].cve;
      return {
        id: cve.id,
        published: cve.published,
        lastModified: cve.lastModified,
        status: cve.vulnStatus,
        description: NVDUtils.getPrimaryDescription(cve),
        cvssScore: NVDUtils.getHighestCVSSScore(cve),
        isInKEV: NVDUtils.isInKEV(cve),
        references: cve.references.slice(0, 3),
        weaknesses:
          cve.weaknesses?.map((w) => w.description[0]?.value).filter(Boolean) ||
          [],
      };
    } catch (error) {
      return { error: `Failed to fetch CVE: ${error}` };
    }
  },

  async searchCVEs(params: {
    keyword?: string;
    severity?: string;
    days?: number;
    hasKev?: boolean;
    limit?: number;
  }) {
    try {
      const searchParams: any = {
        resultsPerPage: Math.min(params.limit || 10, 20),
      };

      if (params.keyword) searchParams.keywordSearch = params.keyword;
      if (params.severity) searchParams.cvssV3Severity = params.severity;
      if (params.hasKev) searchParams.hasKev = true;

      if (params.days) {
        const dateRange = NVDUtils.getLastNDays(params.days);
        searchParams.pubStartDate = dateRange.startDate;
        searchParams.pubEndDate = dateRange.endDate;
      }

      const response = await nvdApi.searchCVEs(searchParams);

      return {
        total: response.totalResults,
        count: response.vulnerabilities.length,
        results: response.vulnerabilities.map((vuln) => {
          const cve = vuln.cve;
          return {
            id: cve.id,
            published: cve.published,
            description:
              NVDUtils.getPrimaryDescription(cve).substring(0, 200) + "...",
            cvssScore: NVDUtils.getHighestCVSSScore(cve),
            isInKEV: NVDUtils.isInKEV(cve),
            status: cve.vulnStatus,
          };
        }),
      };
    } catch (error) {
      return { error: `Failed to search CVEs: ${error}` };
    }
  },

  async getCriticalCVEs(days: number = 30) {
    try {
      const dateRange = NVDUtils.getLastNDays(days);
      const response = await nvdApi.getCVEsBySeverity("CRITICAL", 3, {
        pubStartDate: dateRange.startDate,
        pubEndDate: dateRange.endDate,
        resultsPerPage: 20,
      });

      return {
        total: response.totalResults,
        days,
        results: response.vulnerabilities.map((vuln) => {
          const cve = vuln.cve;
          return {
            id: cve.id,
            published: cve.published,
            description:
              NVDUtils.getPrimaryDescription(cve).substring(0, 150) + "...",
            cvssScore: NVDUtils.getHighestCVSSScore(cve),
            isInKEV: NVDUtils.isInKEV(cve),
          };
        }),
      };
    } catch (error) {
      return { error: `Failed to fetch critical CVEs: ${error}` };
    }
  },

  async getKEVVulnerabilities(limit: number = 10) {
    try {
      const response = await nvdApi.searchCVEs({
        hasKev: true,
        resultsPerPage: Math.min(limit, 20),
      });

      return {
        total: response.totalResults,
        count: response.vulnerabilities.length,
        results: response.vulnerabilities.map((vuln) => {
          const cve = vuln.cve;
          return {
            id: cve.id,
            published: cve.published,
            description:
              NVDUtils.getPrimaryDescription(cve).substring(0, 150) + "...",
            cvssScore: NVDUtils.getHighestCVSSScore(cve),
            cisaActionDue: cve.cisaActionDue,
            cisaRequiredAction: cve.cisaRequiredAction,
            cisaVulnerabilityName: cve.cisaVulnerabilityName,
          };
        }),
      };
    } catch (error) {
      return { error: `Failed to fetch KEV CVEs: ${error}` };
    }
  },
};

// Simple chat handler that demonstrates tool calling
async function handleChatMessage(message: string) {
  const lowerMessage = message.toLowerCase();
  console.log(`\n🤖 Processing: "${message}"`);

  // Detect intent and call appropriate tools
  if (lowerMessage.includes("cve-") && lowerMessage.match(/cve-\d{4}-\d+/)) {
    // Extract CVE ID
    const cveMatch = message.match(/CVE-\d{4}-\d+/i);
    if (cveMatch) {
      const cveId = cveMatch[0];
      console.log(`🔧 Calling tool: getCVEById(${cveId})`);
      const result = await nvdTools.getCVEById(cveId);
      return {
        message: `Here's information about ${cveId}:`,
        toolCall: { tool: "getCVEById", parameters: { cveId }, result },
      };
    }
  }

  if (
    lowerMessage.includes("critical") &&
    (lowerMessage.includes("vulnerabilities") || lowerMessage.includes("cves"))
  ) {
    console.log(`🔧 Calling tool: getCriticalCVEs(30)`);
    const result = await nvdTools.getCriticalCVEs(30);
    return {
      message: "Here are the critical vulnerabilities from the last 30 days:",
      toolCall: { tool: "getCriticalCVEs", parameters: { days: 30 }, result },
    };
  }

  if (
    lowerMessage.includes("kev") ||
    lowerMessage.includes("known exploited")
  ) {
    console.log(`🔧 Calling tool: getKEVVulnerabilities(10)`);
    const result = await nvdTools.getKEVVulnerabilities(10);
    return {
      message:
        "Here are vulnerabilities from CISA's Known Exploited Vulnerabilities catalog:",
      toolCall: {
        tool: "getKEVVulnerabilities",
        parameters: { limit: 10 },
        result,
      },
    };
  }

  if (lowerMessage.includes("search") || lowerMessage.includes("find")) {
    // Extract search terms
    const keywords = message
      .replace(/search|find|for|vulnerabilities|cves/gi, "")
      .trim();
    if (keywords) {
      console.log(
        `🔧 Calling tool: searchCVEs({ keyword: "${keywords}", limit: 10 })`
      );
      const result = await nvdTools.searchCVEs({
        keyword: keywords,
        limit: 10,
      });
      return {
        message: `Here are CVEs related to "${keywords}":`,
        toolCall: {
          tool: "searchCVEs",
          parameters: { keyword: keywords, limit: 10 },
          result,
        },
      };
    }
  }

  // Check for severity and time-based queries
  if (lowerMessage.includes("high") && lowerMessage.includes("7 days")) {
    console.log(
      `🔧 Calling tool: searchCVEs({ severity: "HIGH", days: 7, limit: 10 })`
    );
    const result = await nvdTools.searchCVEs({
      severity: "HIGH",
      days: 7,
      limit: 10,
    });
    return {
      message: "Here are HIGH severity CVEs from the last 7 days:",
      toolCall: {
        tool: "searchCVEs",
        parameters: { severity: "HIGH", days: 7, limit: 10 },
        result,
      },
    };
  }

  // Default response with available commands
  return {
    message: `I can help you with vulnerability information! Try asking me:
    
🔍 "Search for Microsoft vulnerabilities"
🚨 "Show me critical vulnerabilities"
📋 "Get CVE-2021-44228 details"
⚠️  "Show KEV vulnerabilities"
📈 "Find high severity CVEs from last 7 days"

What would you like to know about vulnerabilities?`,
    toolCall: null,
  };
}

// Demo function to show various chat interactions
async function runChatDemo() {
  console.log("🤖 NVD AI Chat Demo");
  console.log("=".repeat(50));

  const testMessages = [
    "Hello, what can you do?",
    "Show me critical vulnerabilities",
    "Get CVE-2021-44228 details",
    "Search for Microsoft vulnerabilities",
    "Show KEV vulnerabilities",
    "Find high severity CVEs from last 7 days",
  ];

  for (const message of testMessages) {
    try {
      const response = await handleChatMessage(message);

      console.log(`\n💬 Response: ${response.message}`);

      if (response.toolCall) {
        console.log(`\n🔧 Tool Used: ${response.toolCall.tool}`);
        console.log(
          `📋 Parameters:`,
          JSON.stringify(response.toolCall.parameters, null, 2)
        );

        if (response.toolCall.result.error) {
          console.log(`❌ Error: ${response.toolCall.result.error}`);
        } else {
          console.log(`✅ Results:`);

          if (
            response.toolCall.result &&
            Array.isArray((response.toolCall.result as any).results)
          ) {
            const resultObj = response.toolCall.result as {
              results: any[];
              count?: number;
              total?: number;
            };
            console.log(
              `   Found ${resultObj.count || 0} of ${
                resultObj.total || 0
              } total`
            );
            resultObj.results
              .slice(0, 3)
              .forEach((item: any, index: number) => {
                console.log(
                  `   ${index + 1}. ${item.id} - Score: ${
                    item.cvssScore || "N/A"
                  }`
                );
                if (item.description) {
                  console.log(`      ${item.description.substring(0, 80)}...`);
                }
              });
          } else if (
            response.toolCall.result &&
            (response.toolCall.result as any).id
          ) {
            // Single CVE result
            const cve = response.toolCall.result as any;
            console.log(`   CVE: ${cve.id}`);
            console.log(`   Score: ${cve.cvssScore || "N/A"}`);
            console.log(`   Status: ${cve.status}`);
            console.log(`   KEV: ${cve.isInKEV ? "Yes" : "No"}`);
            console.log(
              `   Description: ${cve.description.substring(0, 100)}...`
            );
          }
        }
      }

      console.log("\n" + "-".repeat(50));

      // Add delay between requests to respect rate limits
      await new Promise((resolve) => setTimeout(resolve, 2000));
    } catch (error) {
      console.error(`❌ Error processing "${message}":`, error);
    }
  }

  console.log("\n✅ Chat demo completed!");
  console.log("\n📝 This demonstrates how an AI model would:");
  console.log("   1. Parse user intent from natural language");
  console.log("   2. Select appropriate tools to call");
  console.log("   3. Execute tools with extracted parameters");
  console.log("   4. Format and present results to the user");
}

// Export for use in other files
export { nvdTools, handleChatMessage };

// Run demo if this file is executed directly
if (require.main === module) {
  runChatDemo().catch(console.error);
}
