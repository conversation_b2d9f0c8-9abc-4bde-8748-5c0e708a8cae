import { NVDApiClient, nvdApi, NVDUtils, CVESearchParams } from './nvd-api';

/**
 * Example usage of NVD API functions
 * This file demonstrates various ways to interact with the NVD API
 */

// Example 1: Basic CVE search
async function basicCVESearch() {
  console.log('=== Basic CVE Search ===');
  
  try {
    // Search for recent CVEs (last 10 results)
    const response = await nvdApi.searchCVEs({
      resultsPerPage: 10,
      startIndex: 0
    });
    
    console.log(`Found ${response.totalResults} total CVEs`);
    console.log(`Showing ${response.vulnerabilities.length} results:`);
    
    response.vulnerabilities.forEach((vuln, index) => {
      const cve = vuln.cve;
      const score = NVDUtils.getHighestCVSSScore(cve);
      const description = NVDUtils.getPrimaryDescription(cve);
      
      console.log(`${index + 1}. ${cve.id}`);
      console.log(`   Score: ${score || 'N/A'}`);
      console.log(`   Status: ${cve.vulnStatus}`);
      console.log(`   Description: ${description.substring(0, 100)}...`);
      console.log(`   Published: ${cve.published}`);
      console.log('');
    });
  } catch (error) {
    console.error('Error in basic search:', error);
  }
}

// Example 2: Search by specific CVE ID
async function searchSpecificCVE() {
  console.log('=== Search Specific CVE ===');
  
  try {
    // Search for Log4j vulnerability
    const response = await nvdApi.getCVEById('CVE-2021-44228');
    
    if (response.vulnerabilities.length > 0) {
      const cve = response.vulnerabilities[0].cve;
      console.log(`CVE ID: ${cve.id}`);
      console.log(`Published: ${cve.published}`);
      console.log(`Last Modified: ${cve.lastModified}`);
      console.log(`Status: ${cve.vulnStatus}`);
      console.log(`Description: ${NVDUtils.getPrimaryDescription(cve)}`);
      
      // Show CVSS scores
      if (cve.metrics?.cvssMetricV31) {
        cve.metrics.cvssMetricV31.forEach(metric => {
          console.log(`CVSS v3.1 Score: ${metric.cvssData.baseScore} (${metric.cvssData.baseSeverity})`);
          console.log(`Vector: ${metric.cvssData.vectorString}`);
        });
      }
      
      // Show references
      console.log('\nReferences:');
      cve.references.slice(0, 5).forEach(ref => {
        console.log(`- ${ref.url}`);
      });
      
      // Check if in CISA KEV
      if (NVDUtils.isInKEV(cve)) {
        console.log('\n⚠️  This CVE is in CISA\'s Known Exploited Vulnerabilities catalog!');
        if (cve.cisaActionDue) {
          console.log(`Action Due: ${cve.cisaActionDue}`);
        }
        if (cve.cisaRequiredAction) {
          console.log(`Required Action: ${cve.cisaRequiredAction}`);
        }
      }
    }
  } catch (error) {
    console.error('Error searching specific CVE:', error);
  }
}

// Example 3: Search by keyword
async function searchByKeyword() {
  console.log('=== Search by Keyword ===');
  
  try {
    // Search for Microsoft vulnerabilities
    const response = await nvdApi.searchCVEsByKeyword('Microsoft Windows', false, {
      resultsPerPage: 5,
      cvssV3Severity: 'CRITICAL'
    });
    
    console.log(`Found ${response.totalResults} Microsoft Windows CVEs with CRITICAL severity`);
    
    response.vulnerabilities.forEach((vuln, index) => {
      const cve = vuln.cve;
      const score = NVDUtils.getHighestCVSSScore(cve);
      
      console.log(`${index + 1}. ${cve.id} - Score: ${score}`);
      console.log(`   ${NVDUtils.getPrimaryDescription(cve).substring(0, 150)}...`);
      console.log('');
    });
  } catch (error) {
    console.error('Error in keyword search:', error);
  }
}

// Example 4: Search by severity
async function searchBySeverity() {
  console.log('=== Search by Severity ===');
  
  try {
    // Get recent critical vulnerabilities
    const dateRange = NVDUtils.getLastNDays(30);
    
    const response = await nvdApi.getCVEsBySeverity('CRITICAL', 3, {
      pubStartDate: dateRange.startDate,
      pubEndDate: dateRange.endDate,
      resultsPerPage: 10
    });
    
    console.log(`Found ${response.totalResults} CRITICAL CVEs in the last 30 days`);
    
    response.vulnerabilities.forEach((vuln, index) => {
      const cve = vuln.cve;
      const score = NVDUtils.getHighestCVSSScore(cve);
      
      console.log(`${index + 1}. ${cve.id} - Score: ${score}`);
      console.log(`   Published: ${new Date(cve.published).toLocaleDateString()}`);
      console.log(`   ${NVDUtils.getPrimaryDescription(cve).substring(0, 100)}...`);
      
      if (NVDUtils.isInKEV(cve)) {
        console.log('   🚨 IN CISA KEV CATALOG');
      }
      console.log('');
    });
  } catch (error) {
    console.error('Error in severity search:', error);
  }
}

// Example 5: Search by CPE (Common Platform Enumeration)
async function searchByCPE() {
  console.log('=== Search by CPE ===');
  
  try {
    // Search for vulnerabilities affecting Apache HTTP Server
    const response = await nvdApi.getCVEsByCPE(
      'cpe:2.3:a:apache:http_server:2.4.41:*:*:*:*:*:*:*',
      true, // Only vulnerable
      { resultsPerPage: 5 }
    );
    
    console.log(`Found ${response.totalResults} CVEs affecting Apache HTTP Server 2.4.41`);
    
    response.vulnerabilities.forEach((vuln, index) => {
      const cve = vuln.cve;
      const score = NVDUtils.getHighestCVSSScore(cve);
      
      console.log(`${index + 1}. ${cve.id} - Score: ${score || 'N/A'}`);
      console.log(`   ${NVDUtils.getPrimaryDescription(cve).substring(0, 120)}...`);
      console.log('');
    });
  } catch (error) {
    console.error('Error in CPE search:', error);
  }
}

// Example 6: Get CVE change history
async function getCVEHistory() {
  console.log('=== CVE Change History ===');
  
  try {
    // Get change history for a specific CVE
    const response = await nvdApi.getCVEHistoryById('CVE-2021-44228');
    
    console.log(`Found ${response.totalResults} change events for CVE-2021-44228`);
    
    response.cveChanges.forEach((changeWrapper, index) => {
      const change = changeWrapper.change;
      console.log(`${index + 1}. Event: ${change.eventName}`);
      console.log(`   Date: ${new Date(change.created).toLocaleString()}`);
      console.log(`   Source: ${change.sourceIdentifier}`);
      
      if (change.details.length > 0) {
        console.log('   Changes:');
        change.details.slice(0, 3).forEach(detail => {
          console.log(`     - ${detail.action} ${detail.type}`);
          if (detail.newValue) {
            console.log(`       New: ${detail.newValue.substring(0, 80)}...`);
          }
        });
      }
      console.log('');
    });
  } catch (error) {
    console.error('Error getting CVE history:', error);
  }
}

// Example 7: Search for CISA KEV vulnerabilities
async function searchKEVVulnerabilities() {
  console.log('=== CISA KEV Vulnerabilities ===');
  
  try {
    const response = await nvdApi.searchCVEs({
      hasKev: true,
      resultsPerPage: 10
    });
    
    console.log(`Found ${response.totalResults} CVEs in CISA KEV catalog`);
    
    response.vulnerabilities.forEach((vuln, index) => {
      const cve = vuln.cve;
      const score = NVDUtils.getHighestCVSSScore(cve);
      
      console.log(`${index + 1}. ${cve.id} - Score: ${score || 'N/A'}`);
      console.log(`   ${NVDUtils.getPrimaryDescription(cve).substring(0, 100)}...`);
      
      if (cve.cisaActionDue) {
        console.log(`   🚨 CISA Action Due: ${new Date(cve.cisaActionDue).toLocaleDateString()}`);
      }
      if (cve.cisaRequiredAction) {
        console.log(`   Required Action: ${cve.cisaRequiredAction}`);
      }
      console.log('');
    });
  } catch (error) {
    console.error('Error searching KEV vulnerabilities:', error);
  }
}

// Example 8: Advanced search with multiple filters
async function advancedSearch() {
  console.log('=== Advanced Search ===');
  
  try {
    const dateRange = NVDUtils.getLastNDays(7);
    
    const params: CVESearchParams = {
      keywordSearch: 'remote code execution',
      cvssV3Severity: 'HIGH',
      pubStartDate: dateRange.startDate,
      pubEndDate: dateRange.endDate,
      noRejected: true,
      resultsPerPage: 5
    };
    
    const response = await nvdApi.searchCVEs(params);
    
    console.log(`Advanced search found ${response.totalResults} CVEs`);
    console.log('Criteria: Remote code execution, HIGH severity, last 7 days, no rejected CVEs');
    
    response.vulnerabilities.forEach((vuln, index) => {
      const cve = vuln.cve;
      const score = NVDUtils.getHighestCVSSScore(cve);
      
      console.log(`${index + 1}. ${cve.id} - Score: ${score}`);
      console.log(`   Published: ${new Date(cve.published).toLocaleDateString()}`);
      console.log(`   ${NVDUtils.getPrimaryDescription(cve).substring(0, 120)}...`);
      
      // Show weaknesses (CWE)
      if (cve.weaknesses && cve.weaknesses.length > 0) {
        const weakness = cve.weaknesses[0].description[0]?.value;
        console.log(`   Weakness: ${weakness}`);
      }
      console.log('');
    });
  } catch (error) {
    console.error('Error in advanced search:', error);
  }
}

// Example 9: Using API with authentication (if you have an API key)
async function authenticatedExample() {
  console.log('=== Authenticated API Usage ===');
  
  // Create a new client with API key (replace with your actual API key)
  const authenticatedClient = new NVDApiClient('your-api-key-here');
  
  try {
    const response = await authenticatedClient.searchCVEs({
      resultsPerPage: 5
    });
    
    console.log(`Authenticated request returned ${response.vulnerabilities.length} results`);
    console.log('Note: With API key, you get higher rate limits and better performance');
  } catch (error) {
    console.log('Note: This example requires a valid API key from NVD');
    console.log('You can request one at: https://nvd.nist.gov/developers/request-an-api-key');
  }
}

// Example 10: Bulk data processing with progress tracking
async function bulkDataExample() {
  console.log('=== Bulk Data Processing ===');
  
  try {
    console.log('Starting bulk download of recent CVEs...');
    
    const dateRange = NVDUtils.getLastNDays(3); // Last 3 days to keep it manageable
    
    const allCVEs = await nvdApi.getAllCVEs(
      {
        pubStartDate: dateRange.startDate,
        pubEndDate: dateRange.endDate
      },
      (current, total) => {
        const percentage = ((current / total) * 100).toFixed(1);
        console.log(`Progress: ${current}/${total} (${percentage}%)`);
      }
    );
    
    console.log(`\nDownloaded ${allCVEs.length} CVEs from the last 3 days`);
    
    // Analyze the data
    const criticalCVEs = allCVEs.filter(vuln => {
      const score = NVDUtils.getHighestCVSSScore(vuln.cve);
      return score && score >= 9.0;
    });
    
    const kevCVEs = allCVEs.filter(vuln => NVDUtils.isInKEV(vuln.cve));
    
    console.log(`Analysis:`);
    console.log(`- Critical CVEs (score >= 9.0): ${criticalCVEs.length}`);
    console.log(`- CVEs in CISA KEV: ${kevCVEs.length}`);
    
    if (criticalCVEs.length > 0) {
      console.log('\nTop 3 Critical CVEs:');
      criticalCVEs.slice(0, 3).forEach((vuln, index) => {
        const cve = vuln.cve;
        const score = NVDUtils.getHighestCVSSScore(cve);
        console.log(`${index + 1}. ${cve.id} - Score: ${score}`);
        console.log(`   ${NVDUtils.getPrimaryDescription(cve).substring(0, 100)}...`);
      });
    }
  } catch (error) {
    console.error('Error in bulk data processing:', error);
  }
}

// Main function to run all examples
async function runAllExamples() {
  console.log('🚀 Starting NVD API Examples\n');
  
  const examples = [
    basicCVESearch,
    searchSpecificCVE,
    searchByKeyword,
    searchBySeverity,
    searchByCPE,
    getCVEHistory,
    searchKEVVulnerabilities,
    advancedSearch,
    authenticatedExample,
    // bulkDataExample // Commented out as it can take a while
  ];
  
  for (const example of examples) {
    try {
      await example();
      console.log('\n' + '='.repeat(50) + '\n');
      
      // Add delay between examples to respect rate limits
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      console.error(`Error in ${example.name}:`, error);
      console.log('\n' + '='.repeat(50) + '\n');
    }
  }
  
  console.log('✅ All examples completed!');
}

// Export functions for individual use
export {
  basicCVESearch,
  searchSpecificCVE,
  searchByKeyword,
  searchBySeverity,
  searchByCPE,
  getCVEHistory,
  searchKEVVulnerabilities,
  advancedSearch,
  authenticatedExample,
  bulkDataExample,
  runAllExamples
};

// Run examples if this file is executed directly
if (require.main === module) {
  runAllExamples().catch(console.error);
}
